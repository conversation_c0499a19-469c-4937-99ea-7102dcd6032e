{"name": "fuse-react-app", "version": "16.0.0", "private": true, "type": "module", "dependencies": {"@aws-amplify/auth": "6.12.2", "@aws-amplify/ui-react": "6.11.0", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.7", "@hookform/resolvers": "5.0.1", "@mui/base": "5.0.0-beta.70", "@mui/icons-material": "7.2.0", "@mui/lab": "7.0.0-beta.14", "@mui/material": "7.2.0", "@mui/system": "7.2.0", "@mui/utils": "7.2.0", "@mui/x-data-grid": "8.7.0", "@mui/x-date-pickers": "8.7.0", "@popperjs/core": "2.11.8", "@tanstack/react-query": "^5.74.7", "@tanstack/react-query-devtools": "^5.74.7", "@tiptap/extension-highlight": "2.11.7", "@tiptap/extension-image": "2.11.7", "@tiptap/extension-link": "2.11.7", "@tiptap/extension-subscript": "2.11.7", "@tiptap/extension-superscript": "2.11.7", "@tiptap/extension-task-item": "2.11.7", "@tiptap/extension-task-list": "2.11.7", "@tiptap/extension-text-align": "2.11.7", "@tiptap/extension-typography": "2.11.7", "@tiptap/extension-underline": "2.11.7", "@tiptap/pm": "2.11.7", "@tiptap/react": "2.11.7", "@tiptap/starter-kit": "2.11.7", "autosuggest-highlight": "3.3.4", "aws-amplify": "6.14.2", "clsx": "2.1.1", "crypto-js": "4.2.0", "date-fns": "4.1.0", "firebase": "11.6.0", "history": "5.3.0", "i18next": "25.0.1", "jwt-decode": "3.1.2", "ky": "1.8.1", "lodash": "4.17.21", "marked": "4.3.0", "material-react-table": "3.2.1", "mobile-detect": "1.4.5", "moment": "2.30.1", "motion": "12.20.5", "msw": "2.7.5", "notistack": "3.0.2", "perfect-scrollbar": "1.5.6", "prismjs": "1.30.0", "qs": "6.14.0", "react": "19.1.0", "react-app-alias": "2.2.2", "react-autosuggest": "10.1.0", "react-dom": "19.1.0", "react-hook-form": "7.56.0", "react-i18next": "15.4.1", "react-popper": "2.3.0", "react-router": "7.5.2", "react-swipeable": "7.0.2", "styled-components": "6.1.17", "stylis": "4.3.6", "stylis-plugin-rtl": "2.1.1", "type-fest": "4.40.0", "uuid": "11.1.0", "zod": "3.24.3"}, "peerDependencies": {"postcss": "8.5.3", "react": "19.1.0", "react-dom": "19.1.0"}, "overrides": {"react": "19.1.0", "react-dom": "19.1.0", "semver": "7.5.4"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.25.0", "@hookform/devtools": "4.4.0", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/typography": "0.5.16", "@tailwindcss/vite": "4.1.4", "@types/autosuggest-highlight": "3.2.3", "@types/crypto-js": "4.2.2", "@types/lodash": "4.17.16", "@types/node": "22.14.1", "@types/prismjs": "1.26.5", "@types/qs": "6.9.18", "@types/react": "19.1.2", "@types/react-autosuggest": "10.1.11", "@types/react-dom": "19.1.2", "@types/styled-components": "5.1.34", "@types/stylis": "4.2.7", "@typescript-eslint/eslint-plugin": "8.30.1", "@vitejs/plugin-react": "4.4.1", "eslint": "9.25.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "eslint-plugin-unused-imports": "4.1.4", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "promise": "8.3.0", "sass-embedded": "1.86.3", "tailwindcss": "4.1.4", "typescript": "5.8.3", "typescript-eslint": "8.30.1", "vite": "^6.3.5", "vite-plugin-svgr": "4.3.0", "vite-tsconfig-paths": "5.1.4"}, "scripts": {"start": "npm run dev", "dev": "vite", "build": "NODE_OPTIONS=--max-old-space-size=4096 tsc && vite build", "lint": "eslint ./src --config ./eslint.config.mjs", "lint:fix": "eslint --fix ./src --config ./eslint.config.mjs", "preview": "vite preview", "postinstall": "node src/utils/node-scripts/fuse-react-message.js", "poststart": "node src/utils/node-scripts/fuse-react-message.js", "audit": "npm audit --production"}, "engines": {"node": ">=22.12.0", "npm": ">=10.9.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 3 safari version"]}, "msw": {"workerDirectory": ["public"]}}